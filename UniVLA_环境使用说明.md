# UniVLA Conda环境使用说明

## 🎉 环境创建成功！

您的UniVLA专用conda环境已经成功创建并配置完成。以下是详细的使用说明。

## 📋 环境信息

- **环境名称**: `uni_vla_env`
- **Python版本**: 3.10
- **PyTorch版本**: 2.2.0+cu121
- **CUDA版本**: 12.1
- **GPU支持**: ✅ (检测到 NVIDIA RTX A5000)

## 🚀 如何使用环境

### 1. 激活环境
```bash
conda activate uni_vla_env
```

### 2. 进入项目目录
```bash
cd /home/<USER>/johnny_ws/uni_vla_ws/UniVLA
```

### 3. 验证环境
```bash
python /home/<USER>/johnny_ws/uni_vla_ws/test_environment.py
```

## 📦 已安装的主要依赖包

### 核心深度学习框架
- **PyTorch**: 2.2.0+cu121 (支持CUDA 12.1)
- **TorchVision**: 0.17.0+cu121
- **Transformers**: 4.40.1 (Hugging Face)
- **TensorFlow**: 2.15.0

### UniVLA专用模块
- **Prismatic**: 0.0.1 (UniVLA核心模块)
- **Flash Attention 2**: 2.5.5 (高效注意力机制)

### 机器人学习框架
- **RoboSuite**: 1.4.1 (机器人仿真环境)
- **OpenAI Gym**: 0.26.2 (强化学习环境)

### 数据处理和工具
- **NumPy**: 1.26.4
- **OpenCV**: *********
- **HDF5**: 3.11.0
- **ImageIO**: 2.34.2
- **Matplotlib**: 3.10.1

### 配置和实用工具
- **Hydra**: 1.3.2 (配置管理)
- **OmegaConf**: 2.3.0 (配置解析)
- **Draccus**: 0.8.0 (数据类配置)
- **Rich**: 14.0.0 (终端美化)

## 🛠️ 常用操作

### 模型训练
```bash
# 激活环境
conda activate uni_vla_env
cd /home/<USER>/johnny_ws/uni_vla_ws/UniVLA

# 运行训练脚本（根据README中的说明）
bash ./vla-scripts/train.sh
```

### 模型推理
```bash
# 激活环境
conda activate uni_vla_env
cd /home/<USER>/johnny_ws/uni_vla_ws/UniVLA

# 加载模型进行推理
python -c "from prismatic.models import load; model = load('your_model_path')"
```

### 环境管理
```bash
# 查看已安装的包
conda list

# 安装额外的包
pip install package_name

# 导出环境配置
conda env export > environment.yml

# 退出环境
conda deactivate
```

## 🔧 故障排除

### 1. CUDA相关问题
如果遇到CUDA相关错误，请检查：
- CUDA驱动是否正确安装
- PyTorch CUDA版本是否与系统CUDA版本兼容

### 2. 内存不足
如果训练时遇到内存不足：
- 减小batch size
- 使用梯度累积
- 启用混合精度训练

### 3. 依赖冲突
如果遇到包依赖冲突：
```bash
conda activate uni_vla_env
pip check  # 检查依赖冲突
```

## 📚 相关资源

- **UniVLA项目**: `/home/<USER>/johnny_ws/uni_vla_ws/UniVLA`
- **README文档**: `/home/<USER>/johnny_ws/uni_vla_ws/UniVLA/README.md`
- **配置文件**: `/home/<USER>/johnny_ws/uni_vla_ws/UniVLA/prismatic/conf/`
- **训练脚本**: `/home/<USER>/johnny_ws/uni_vla_ws/UniVLA/vla-scripts/`

## 🎯 下一步建议

1. **阅读项目文档**: 详细了解UniVLA的使用方法
2. **准备数据集**: 根据需要下载和准备训练数据
3. **配置训练参数**: 修改配置文件以适应您的需求
4. **开始实验**: 运行训练和推理实验

## ⚠️ 注意事项

1. **环境隔离**: 始终在激活的conda环境中工作
2. **GPU监控**: 使用`nvidia-smi`监控GPU使用情况
3. **定期备份**: 定期备份重要的模型和配置文件
4. **版本兼容**: 安装新包时注意版本兼容性

---

🎉 **恭喜！您的UniVLA环境已经准备就绪，可以开始您的机器人学习之旅了！**

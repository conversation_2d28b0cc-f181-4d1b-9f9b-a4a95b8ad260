#!/usr/bin/env python3
"""
UniVLA环境测试脚本
用于验证conda环境是否正确配置
"""

import sys
import importlib

def test_import(module_name, description=""):
    """测试模块导入"""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✅ {module_name} ({description}): {version}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} ({description}): 导入失败 - {e}")
        return False

def main():
    print("🔍 UniVLA环境验证测试")
    print("=" * 50)
    
    # 核心深度学习框架
    print("\n📦 核心深度学习框架:")
    test_import("torch", "PyTorch")
    test_import("torchvision", "TorchVision")
    test_import("transformers", "Hugging Face Transformers")
    test_import("tensorflow", "TensorFlow")
    
    # UniVLA相关模块
    print("\n🤖 UniVLA相关模块:")
    test_import("prismatic", "Prismatic (UniVLA核心)")
    test_import("flash_attn", "Flash Attention 2")
    
    # 机器人学习相关
    print("\n🦾 机器人学习框架:")
    test_import("robosuite", "RoboSuite")
    test_import("gym", "OpenAI Gym")
    
    # 数据处理和工具
    print("\n🛠️ 数据处理和工具:")
    test_import("numpy", "NumPy")
    test_import("opencv_python", "OpenCV")
    test_import("h5py", "HDF5")
    test_import("imageio", "ImageIO")
    test_import("matplotlib", "Matplotlib")
    
    # 配置和实用工具
    print("\n⚙️ 配置和实用工具:")
    test_import("hydra", "Hydra")
    test_import("omegaconf", "OmegaConf")
    test_import("draccus", "Draccus")
    test_import("rich", "Rich")
    
    # PyTorch和CUDA测试
    print("\n🚀 PyTorch和CUDA测试:")
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ CUDA版本: {torch.version.cuda}")
            print(f"✅ GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
    except Exception as e:
        print(f"❌ PyTorch/CUDA测试失败: {e}")
    
    # 简单的模型加载测试
    print("\n🧠 模型加载测试:")
    try:
        from prismatic.models import load
        print("✅ Prismatic模型加载模块导入成功")
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 环境验证完成！")
    print("\n💡 使用说明:")
    print("1. 激活环境: conda activate uni_vla_env")
    print("2. 进入项目目录: cd UniVLA")
    print("3. 开始使用UniVLA进行微调和推理")

if __name__ == "__main__":
    main()
